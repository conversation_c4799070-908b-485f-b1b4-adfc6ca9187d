"use client"

import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ChevronDown } from "lucide-react"
import { Navigation } from "./components/navigation"

export default function CountryPage() {
  const countryName = "China"

  return (
    <div className="min-h-screen bg-zinc-800">
      <Navigation />

      {/* Main Content */}
      <div className="container mx-auto px-6 py-12">
        {/* Back Button */}
        <div className="mb-12">
          <Button
            variant="outline"
            className="text-white border-white hover:bg-red-500 hover:border-red-500 transition-colors bg-transparent"
          >
            ← Back
          </Button>
        </div>

        {/* Country Title */}
        <div className="text-center mb-12">
          <h1 className="text-6xl font-bold text-white mb-4">{countryName}</h1>
          <div className="w-24 h-1 bg-red-500 mx-auto"></div>
        </div>

        {/* Centered Dropdown Navigation */}
        <div className="flex justify-center mb-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl w-full">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full h-12 text-base font-medium border border-white/40 text-white hover:bg-red-500 hover:border-red-500 transition-all duration-300 flex items-center justify-center px-6 bg-transparent rounded-sm"
                >
                  Actors
                  <ChevronDown className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-80 p-2 bg-zinc-800 border-zinc-700">
                <DropdownMenuItem className="text-base py-3 text-white hover:bg-red-500 hover:text-white group relative">
                  <div>
                    <div className="font-medium">State Actors</div>
                    <div className="text-sm text-gray-400 group-hover:text-white mt-1">
                      Government officials, military leaders, and state institutions that shape national policy and
                      international relations.
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-base py-3 text-white hover:bg-red-500 hover:text-white group relative">
                  <div>
                    <div className="font-medium">Political Groups</div>
                    <div className="text-sm text-gray-400 group-hover:text-white mt-1">
                      Political parties, opposition movements, and civil society organizations influencing domestic
                      politics.
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-base py-3 text-white hover:bg-red-500 hover:text-white group relative">
                  <div>
                    <div className="font-medium">Economic Actors</div>
                    <div className="text-sm text-gray-400 group-hover:text-white mt-1">
                      Major corporations, business leaders, and economic institutions driving financial and trade
                      policies.
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-base py-3 text-white hover:bg-red-500 hover:text-white group relative">
                  <div>
                    <div className="font-medium">International Actors</div>
                    <div className="text-sm text-gray-400 group-hover:text-white mt-1">
                      Foreign governments, international organizations, and multinational entities affecting bilateral
                      relations.
                    </div>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full h-12 text-base font-medium border border-white/40 text-white hover:bg-red-500 hover:border-red-500 transition-all duration-300 flex items-center justify-center px-6 bg-transparent rounded-sm"
                >
                  General Information
                  <ChevronDown className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-80 p-2 bg-zinc-800 border-zinc-700">
                <DropdownMenuItem className="text-base py-3 text-white hover:bg-red-500 hover:text-white group relative">
                  <div>
                    <div className="font-medium">Society & Culture</div>
                    <div className="text-sm text-gray-400 group-hover:text-white mt-1">
                      Demographics, cultural values, social structures, and ethnic composition shaping national
                      identity.
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-base py-3 text-white hover:bg-red-500 hover:text-white group relative">
                  <div>
                    <div className="font-medium">Economy</div>
                    <div className="text-sm text-gray-400 group-hover:text-white mt-1">
                      Economic indicators, trade relationships, industrial sectors, and financial market developments.
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-base py-3 text-white hover:bg-red-500 hover:text-white group relative">
                  <div>
                    <div className="font-medium">Security & Geopolitics</div>
                    <div className="text-sm text-gray-400 group-hover:text-white mt-1">
                      Military capabilities, defense policies, strategic positioning, and geopolitical influence in
                      regional affairs.
                    </div>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full h-12 text-base font-medium border border-white/40 text-white hover:bg-red-500 hover:border-red-500 transition-all duration-300 flex items-center justify-center px-6 bg-transparent rounded-sm"
                >
                  Power Relations
                  <ChevronDown className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-80 p-2 bg-zinc-800 border-zinc-700">
                <DropdownMenuItem className="text-base py-3 text-white hover:bg-red-500 hover:text-white group relative">
                  <div>
                    <div className="font-medium">Power Relations between Actors</div>
                    <div className="text-sm text-gray-400 group-hover:text-white mt-1">
                      Interactions and dynamics between different political, economic, and social actors within the
                      country.
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-base py-3 text-white hover:bg-red-500 hover:text-white group relative">
                  <div>
                    <div className="font-medium">Regional Power Relations</div>
                    <div className="text-sm text-gray-400 group-hover:text-white mt-1">
                      Influence and relationships with neighboring countries and regional organizations.
                    </div>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-base py-3 text-white hover:bg-red-500 hover:text-white group relative">
                  <div>
                    <div className="font-medium">International Power Relations</div>
                    <div className="text-sm text-gray-400 group-hover:text-white mt-1">
                      Global diplomatic ties, strategic partnerships, and international positioning.
                    </div>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Country Overview Section */}
        <div className="max-w-7xl mx-auto">
          <div className="border-l-4 border-red-500 pl-10 mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">Country Overview</h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* Left Side - Text Content */}
            <div className="space-y-6">
              <p className="text-xl text-gray-300 leading-relaxed">
                The People's Republic of China stands as one of the world's most influential geopolitical actors,
                wielding significant economic, military, and diplomatic power across multiple regions and international
                forums.
              </p>

              <p className="text-gray-300 leading-relaxed">
                As the world's second-largest economy and most populous nation, China's political landscape is dominated
                by the Chinese Communist Party (CCP), which maintains centralized control over governance,
                policy-making, and strategic direction. The country's leadership structure operates through a complex
                hierarchy of party committees and state institutions.
              </p>

              <p className="text-gray-300 leading-relaxed">
                China's foreign policy approach emphasizes sovereignty, territorial integrity, and non-interference,
                while simultaneously pursuing ambitious initiatives like the Belt and Road Initiative to expand its
                global influence. The nation maintains complex relationships with major powers, balancing cooperation
                and competition across economic, technological, and security domains.
              </p>

              <p className="text-gray-300 leading-relaxed">
                Understanding China's geopolitical position requires analysis of its domestic political dynamics,
                economic transformation, military modernization, and evolving role in international institutions and
                regional security architectures.
              </p>
            </div>

            {/* Right Side - Map */}
            <div className="bg-zinc-700 rounded-lg p-8 border border-zinc-600">
              <div className="aspect-square bg-zinc-600 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="w-32 h-32 bg-red-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span className="text-white text-2xl font-bold">CN</span>
                  </div>
                  <h3 className="text-white text-xl font-semibold mb-2">Detailed Map of China</h3>
                  <p className="text-gray-400 text-sm">
                    Interactive map showing provinces, major cities, and geopolitical regions
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Back to Top Button */}
        <div className="flex justify-center mt-16">
          <Button
            onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
            className="bg-red-500 hover:bg-red-600 text-white px-8 py-3 rounded-lg transition-colors"
          >
            Back to Top ↑
          </Button>
        </div>
      </div>
    </div>
  )
}
