"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Search, Globe, Menu, Database, Info, List, ChevronDown } from "lucide-react" // Import ChevronDown

export function Navigation() {
  return (
    <nav className="bg-zinc-800 border-b border-zinc-700">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="text-2xl font-bold text-red-500 mr-12">GeoPolitica</div>

          {/* Navigation Items */}
          <div className="hidden md:flex items-center space-x-8">
            <Button variant="ghost" className="text-white hover:text-red-500 hover:bg-transparent transition-colors">
              <List className="w-4 h-4 mr-2" />
              Country List
            </Button>

            <Button variant="ghost" className="text-white hover:text-red-500 hover:bg-transparent transition-colors">
              <Database className="w-4 h-4 mr-2" />
              Database
            </Button>

            <Button variant="ghost" className="text-white hover:text-red-500 hover:bg-transparent transition-colors">
              <Info className="w-4 h-4 mr-2" />
              What is GeoPolitica
            </Button>

            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search..."
                className="pl-10 bg-zinc-700 border-zinc-600 text-white placeholder-gray-400 focus:border-red-500 w-48"
              />
            </div>

            {/* Language Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="text-white hover:text-red-500 hover:bg-transparent transition-colors"
                >
                  <Globe className="w-4 h-4 mr-2" />
                  EN
                  <ChevronDown className="h-4 w-4 ml-1" /> {/* Added ChevronDown icon */}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-zinc-800 border-zinc-700">
                <DropdownMenuItem className="text-white hover:bg-zinc-700 hover:text-red-500">English</DropdownMenuItem>
                <DropdownMenuItem className="text-white hover:bg-zinc-700 hover:text-red-500">Español</DropdownMenuItem>
                <DropdownMenuItem className="text-white hover:bg-zinc-700 hover:text-red-500">
                  Français
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Mobile Menu */}
          <Button variant="ghost" className="md:hidden text-white">
            <Menu className="w-6 h-6" />
          </Button>
        </div>
      </div>
    </nav>
  )
}
