"use client"

import type React from "react"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"

interface WorldMapProps {
  onCountrySelect: (country: string) => void
  selectedCountry: string | null
}

export function WorldMap({ onCountrySelect, selectedCountry }: WorldMapProps) {
  const [scale, setScale] = useState(1)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const svgRef = useRef<SVGSVGElement>(null)

  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev * 1.5, 4))
  }

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev / 1.5, 0.5))
  }

  const handleReset = () => {
    setScale(1)
    setPosition({ x: 0, y: 0 })
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y })
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const countries = [
    { name: "United States", path: "M200 150 L350 130 L380 200 L320 250 L250 230 L200 200 Z", color: "#4b5563" },
    { name: "Canada", path: "M200 80 L380 60 L400 130 L350 130 L200 150 Z", color: "#4b5563" },
    { name: "Mexico", path: "M200 250 L320 250 L340 300 L220 320 Z", color: "#4b5563" },
    { name: "Brazil", path: "M350 320 L450 310 L470 450 L380 470 L350 380 Z", color: "#4b5563" },
    { name: "Argentina", path: "M350 450 L420 440 L430 550 L360 560 Z", color: "#4b5563" },
    { name: "United Kingdom", path: "M480 140 L500 135 L505 155 L485 160 Z", color: "#4b5563" },
    { name: "France", path: "M490 160 L520 155 L525 185 L495 190 Z", color: "#4b5563" },
    { name: "Germany", path: "M520 140 L550 135 L555 170 L525 175 Z", color: "#4b5563" },
    { name: "Spain", path: "M470 180 L510 175 L515 205 L475 210 Z", color: "#4b5563" },
    { name: "Italy", path: "M530 180 L550 175 L555 220 L535 225 Z", color: "#4b5563" },
    { name: "Russia", path: "M580 80 L780 70 L800 180 L750 200 L650 160 L580 140 Z", color: "#4b5563" },
    { name: "China", path: "M700 180 L820 170 L840 250 L780 270 L720 230 Z", color: "#4b5563" },
    { name: "India", path: "M680 250 L750 240 L770 320 L700 340 Z", color: "#4b5563" },
    { name: "Japan", path: "M850 200 L880 195 L885 240 L855 245 Z", color: "#4b5563" },
    { name: "Australia", path: "M780 420 L880 410 L890 480 L790 490 Z", color: "#4b5563" },
    { name: "South Africa", path: "M540 420 L590 415 L595 460 L545 465 Z", color: "#4b5563" },
    { name: "Egypt", path: "M540 280 L570 275 L575 310 L545 315 Z", color: "#4b5563" },
    { name: "Nigeria", path: "M500 320 L530 315 L535 345 L505 350 Z", color: "#4b5563" },
    { name: "Turkey", path: "M550 200 L590 195 L595 220 L555 225 Z", color: "#4b5563" },
    { name: "Iran", path: "M600 220 L640 215 L645 250 L605 255 Z", color: "#4b5563" },
    { name: "Saudi Arabia", path: "M580 250 L620 245 L625 290 L585 295 Z", color: "#4b5563" },
    { name: "South Korea", path: "M830 220 L850 215 L855 240 L835 245 Z", color: "#4b5563" },
  ]

  return (
    <div className="relative w-full h-96 lg:h-[500px] bg-zinc-700 rounded-lg overflow-hidden">
      {/* Map Controls */}
      <div className="absolute top-4 right-4 z-10 flex flex-col gap-2">
        <Button
          onClick={handleZoomIn}
          size="sm"
          className="bg-zinc-800 hover:bg-zinc-700 text-white border border-zinc-600"
        >
          +
        </Button>
        <Button
          onClick={handleZoomOut}
          size="sm"
          className="bg-zinc-800 hover:bg-zinc-700 text-white border border-zinc-600"
        >
          -
        </Button>
        <Button
          onClick={handleReset}
          size="sm"
          className="bg-zinc-800 hover:bg-zinc-700 text-white border border-zinc-600 text-xs px-2"
        >
          Reset
        </Button>
      </div>

      {/* World Map SVG */}
      <svg
        ref={svgRef}
        viewBox="0 0 1000 600"
        className="w-full h-full cursor-move"
        style={{
          transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
          transformOrigin: "center",
          background: "#374151",
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* Grid lines for reference */}
        <defs>
          <pattern id="detailed-grid" width="25" height="25" patternUnits="userSpaceOnUse">
            <path d="M 25 0 L 0 0 0 25" fill="none" stroke="#4b5563" strokeWidth="0.5" opacity="0.2" />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#detailed-grid)" />

        {/* Countries */}
        <g className="countries">
          {countries.map((country) => (
            <path
              key={country.name}
              d={country.path}
              fill={selectedCountry === country.name ? "#ef4444" : country.color}
              stroke="#6b7280"
              strokeWidth="1"
              className="hover:fill-red-500 cursor-pointer transition-colors duration-200"
              onClick={(e) => {
                e.stopPropagation()
                onCountrySelect(country.name)
              }}
              style={{ pointerEvents: "auto" }}
            />
          ))}
        </g>

        {/* Ocean areas */}
        <rect x="0" y="0" width="180" height="600" fill="#1f2937" opacity="0.3" />
        <rect x="900" y="0" width="100" height="600" fill="#1f2937" opacity="0.3" />
        <rect x="0" y="0" width="1000" height="50" fill="#1f2937" opacity="0.3" />
        <rect x="0" y="550" width="1000" height="50" fill="#1f2937" opacity="0.3" />
      </svg>

      {/* Map Legend */}
      <div className="absolute bottom-4 left-4 bg-zinc-800/90 p-4 rounded-lg">
        <h3 className="text-sm font-semibold mb-2 text-zinc-300">Interactive Map</h3>
        <div className="flex items-center space-x-2 text-xs text-zinc-400 mb-1">
          <div className="w-4 h-4 bg-zinc-600 rounded"></div>
          <span>Hover to highlight</span>
        </div>
        <div className="flex items-center space-x-2 text-xs text-zinc-400 mb-1">
          <div className="w-4 h-4 bg-red-500 rounded"></div>
          <span>Click to select</span>
        </div>
        <div className="text-xs text-zinc-400">
          <span>Drag to pan • Use +/- to zoom</span>
        </div>
      </div>
    </div>
  )
}
