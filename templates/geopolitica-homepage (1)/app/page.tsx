"use client"

import { useState } from "react"
import { Search, Globe, Database, List, Info, ChevronDown } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { WorldMap } from "@/components/world-map"

const countries = [
  "United States",
  "China",
  "Russia",
  "Germany",
  "United Kingdom",
  "France",
  "Japan",
  "India",
  "Brazil",
  "Canada",
  "Australia",
  "South Korea",
  "Italy",
  "Spain",
  "Mexico",
  "Turkey",
  "Iran",
  "Saudi Arabia",
  "Israel",
  "Egypt",
  "South Africa",
  "Nigeria",
]

const languages = [
  { code: "EN", name: "English" },
  { code: "ES", name: "Español" },
  { code: "FR", name: "Français" },
  { code: "DE", name: "Deutsch" },
  { code: "PT", name: "Português" },
  { code: "RU", name: "Русский" },
  { code: "Z<PERSON>", name: "中文" },
  { code: "AR", name: "العربية" },
]

export default function HomePage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null)
  const [selectedLanguage, setSelectedLanguage] = useState("EN")
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false)

  const filteredCountries = countries.filter((country) => country.toLowerCase().includes(searchQuery.toLowerCase()))

  const handleCountrySelect = (country: string) => {
    setSelectedCountry(country)
    // Here you would navigate to the country's detailed page
    console.log(`Selected country: ${country}`)
  }

  return (
    <div className="min-h-screen bg-zinc-800 text-zinc-100">
      {/* Hero Section */}
      <section className="relative min-h-screen flex flex-col">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
          style={{
            backgroundImage: "url('/placeholder.svg?height=1080&width=1920')",
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-b from-zinc-800/80 via-zinc-800/60 to-zinc-800/80" />

        {/* Navigation */}
        <nav className="relative z-10 flex items-center justify-between p-6 lg:px-12">
          <div className="text-2xl lg:text-3xl font-bold text-red-500">GeoPolitica </div>

          <div className="hidden md:flex items-center space-x-8">
            <Button variant="ghost" className="text-zinc-300 hover:text-red-500 hover:bg-zinc-800 ml-8">
              <List className="w-4 h-4 mr-2" />
              Country List
            </Button>
            <Button variant="ghost" className="text-zinc-300 hover:text-red-500 hover:bg-zinc-800">
              <Database className="w-4 h-4 mr-2" />
              Database
            </Button>
            <Button variant="ghost" className="text-zinc-300 hover:text-red-500 hover:bg-zinc-800">
              <Info className="w-4 h-4 mr-2" />
              What is GeoPolitica
            </Button>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-zinc-400 w-4 h-4" />
              <Input
                placeholder="Search..."
                className="pl-10 bg-zinc-800 border-zinc-700 text-zinc-100 placeholder-zinc-400 focus:border-red-400"
              />
            </div>

            {/* Language Dropdown */}
            <div className="relative">
              <Button
                variant="ghost"
                className="text-zinc-300 hover:text-red-500 hover:bg-zinc-800 flex items-center"
                onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
              >
                <Globe className="w-4 h-4 mr-2" />
                {selectedLanguage}
                <ChevronDown className="w-4 h-4 ml-1" />
              </Button>

              {showLanguageDropdown && (
                <Card className="absolute top-full right-0 mt-2 bg-zinc-800 border-zinc-700 min-w-[120px] z-20">
                  {languages.map((language) => (
                    <button
                      key={language.code}
                      onClick={() => {
                        setSelectedLanguage(language.code)
                        setShowLanguageDropdown(false)
                      }}
                      className="w-full text-left px-4 py-2 hover:bg-zinc-700 text-zinc-100 border-b border-zinc-700 last:border-b-0 transition-colors text-sm"
                    >
                      {language.name}
                    </button>
                  ))}
                </Card>
              )}
            </div>
          </div>
        </nav>

        {/* Hero Content */}
        <div className="relative z-10 flex-1 flex flex-col items-center justify-center px-6 text-center">
          <h1 className="text-4xl lg:text-6xl font-bold mb-6 max-w-4xl text-white">
            The trusted platform for geopolitical data
          </h1>

          <p className="text-xl lg:text-2xl text-zinc-300 mb-8 max-w-3xl leading-relaxed">
            Navigate easily the complex world of global politics
          </p>

          {/* Country Search */}
          <div className="relative w-full max-w-md">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-zinc-400 w-5 h-5" />
            <Input
              placeholder="Search for a country..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 pr-4 py-4 text-lg bg-zinc-800/80 border-zinc-600 text-zinc-100 placeholder-zinc-400 focus:border-red-400 focus:bg-zinc-800"
            />

            {/* Search Results Dropdown */}
            {searchQuery && filteredCountries.length > 0 && (
              <Card className="absolute top-full left-0 right-0 mt-2 bg-zinc-800 border-zinc-700 max-h-60 overflow-y-auto z-20">
                {filteredCountries.slice(0, 8).map((country) => (
                  <button
                    key={country}
                    onClick={() => handleCountrySelect(country)}
                    className="w-full text-left px-4 py-3 hover:bg-zinc-700 text-zinc-100 border-b border-zinc-700 last:border-b-0 transition-colors"
                  >
                    {country}
                  </button>
                ))}
              </Card>
            )}
          </div>

          <div className="mt-12 flex flex-col sm:flex-row gap-4">
            <Button size="lg" className="bg-red-500 hover:bg-red-600 text-white px-8 py-3">
              Explore Countries
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-zinc-600 text-zinc-300 hover:bg-zinc-800 px-8 py-3 bg-transparent"
            >
              View Database
            </Button>
          </div>
        </div>
      </section>

      {/* Interactive Map Section */}
      <section className="min-h-screen bg-zinc-700 py-16">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Explore Global Politics
              <span className="text-white block mt-2">Interactive World Map</span>
            </h2>
            <p className="text-xl text-zinc-300 max-w-2xl mx-auto">
              Click on any country to access detailed geopolitical information, analysis, and data.
            </p>
          </div>

          {/* Map Container */}
          <div className="bg-zinc-800 rounded-lg p-8 shadow-2xl">
            <WorldMap onCountrySelect={handleCountrySelect} selectedCountry={selectedCountry} />

            {/* Selected Country Info */}
            {selectedCountry && (
              <div className="mt-6 p-4 bg-zinc-600 rounded-lg border border-zinc-600">
                <h3 className="text-lg font-semibold text-red-400 mb-2">Selected: {selectedCountry}</h3>
                <p className="text-zinc-300 mb-4">
                  Access comprehensive geopolitical data, political analysis, and current affairs for {selectedCountry}.
                </p>
                <Button className="bg-red-500 hover:bg-red-600">View {selectedCountry} Details</Button>
              </div>
            )}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">{/* Placeholder for Quick Stats */}</div>
        </div>
      </section>
    </div>
  )
}
