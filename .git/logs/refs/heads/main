0000000000000000000000000000000000000000 d3f278cb87029fde9ff32edf3f0c8227af1daed0 frfi <<EMAIL>> 1744574200 +0200	branch: Created from HEAD
d3f278cb87029fde9ff32edf3f0c8227af1daed0 0498f65b0b572282a12159169a5419435700078c frfi <<EMAIL>> 1744574460 +0200	commit: Add deployment configs
0498f65b0b572282a12159169a5419435700078c 8d7cbebb77f510a306b5e5ec93560e79c5f8374a frfi <<EMAIL>> 1745091069 +0200	commit: Remove custom footer and layout fixes
8d7cbebb77f510a306b5e5ec93560e79c5f8374a 12736fc2e75030169f54da5b2dbb5914dc39b7c7 frfi <<EMAIL>> 1745091469 +0200	commit: Add Node collection and related components
12736fc2e75030169f54da5b2dbb5914dc39b7c7 dffca5937ccd6483e89030c3bae8d20529a9cbf7 frfi <<EMAIL>> 1745091553 +0200	commit (amend): Add Node collection and related components
dffca5937ccd6483e89030c3bae8d20529a9cbf7 48849013ffb045a414639773c0c3411b31867d65 frfi <<EMAIL>> 1745092191 +0200	commit: Add Home global configuration and data fetching logic
48849013ffb045a414639773c0c3411b31867d65 dd736894e2d476ceb9fa21806333f0d33360ed7a frfi <<EMAIL>> 1745092470 +0200	commit: Refactor global data fetching functions for improved type safety
dd736894e2d476ceb9fa21806333f0d33360ed7a 75aceedae38327967787e5460eae81b6c5f5a6e5 frfi <<EMAIL>> 1745092526 +0200	commit (amend): Refactor global data fetching functions for improved type safety
75aceedae38327967787e5460eae81b6c5f5a6e5 abf1df161774207c89144091c55e9576479bb514 frfi <<EMAIL>> 1745093631 +0200	commit: Add Lexicon global configuration and data fetching logic
abf1df161774207c89144091c55e9576479bb514 1bfa6570b5fa9a1d4d2fd17621bff60eedb34c39 frfi <<EMAIL>> 1745094025 +0200	commit (amend): Add Lexicon global configuration and data fetching logic
1bfa6570b5fa9a1d4d2fd17621bff60eedb34c39 dbe4ec9f249df964f15446ce4914a8e2c7e4de0e frfi <<EMAIL>> 1745993804 +0200	commit: Update configuration and dependencies for improved build and performance
dbe4ec9f249df964f15446ce4914a8e2c7e4de0e 23035e8d032071ba7c740f75ae65674f7695cc9d frfi <<EMAIL>> 1751205028 +0200	commit: WIP
23035e8d032071ba7c740f75ae65674f7695cc9d 0f1615b67a370b227ef901909d47d0803f9f23a6 frfi <<EMAIL>> 1751205620 +0200	commit (amend): WIP
0f1615b67a370b227ef901909d47d0803f9f23a6 2b4ded0da6cab148504ac8e87823a1a2117b803d frfi <<EMAIL>> 1751205630 +0200	commit (amend): Fixing type issues
2b4ded0da6cab148504ac8e87823a1a2117b803d 653edfcb244341068062c82aee90cd75381d8ac7 frfi <<EMAIL>> 1751207321 +0200	commit: WIP Environment variables
653edfcb244341068062c82aee90cd75381d8ac7 694bfa6c07cf6781c6a0c7413097e3a306ddc8ea frfi <<EMAIL>> 1751263060 +0200	commit: Add migrations
