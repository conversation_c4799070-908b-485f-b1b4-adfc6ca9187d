import type { Metadata } from 'next'

import configPromise from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import Link from 'next/link'
import { getLexiconData } from '@/Lexicon/Component'
import PageClient from './page.client'

export const dynamic = 'force-dynamic'
export const revalidate = 60

export default async function LexiconPage() {
  // Fetch the lexicon global data
  const lexiconData = await getLexiconData()

  // Fetch all published nodes
  const payload = await getPayload({ config: configPromise })
  const nodes = await payload.find({
    collection: 'nodes',
    depth: 0,
    where: {
      _status: {
        equals: 'published',
      },
    },
    sort: ['title']
  })

  return (
    <div className="pt-24 pb-24">
      <PageClient />
      <div className="container mb-16">
        <div className="prose dark:prose-invert max-w-none">
          <h1 className="mb-4">{lexiconData.title}</h1>
          <p className="mb-8 text-lg">{lexiconData.description}</p>
        </div>
      </div>

      <div className="container">
        {nodes.docs.length > 0 ? (
          <div className="grid grid-cols-1 gap-4">
            <ul className="list-disc pl-6 space-y-2">
              {nodes.docs.map((node) => (
                <li key={node.id} className="text-lg">
                  <Link
                    href={`/nodes/${node.slug}`}
                    className="text-primary hover:underline"
                  >
                    {node.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-lg text-muted-foreground">No entries found in the lexicon.</p>
          </div>
        )}
      </div>
    </div>
  )
}

export async function generateMetadata(): Promise<Metadata> {
  const lexiconData = await getLexiconData()

  return {
    title: lexiconData.meta?.title || 'Lexicon - Geopolitica',
    description: lexiconData.meta?.description || 'Browse our comprehensive lexicon of geopolitical terms and concepts.',
  }
}
