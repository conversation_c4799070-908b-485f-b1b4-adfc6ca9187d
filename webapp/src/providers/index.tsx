import React from 'react'

import { HeaderThemeProvider } from './HeaderTheme'
import { RadixThemeProvider } from './RadixTheme'
import { ThemeProvider } from './Theme'

export const Providers: React.FC<{
  children: React.ReactNode
}> = ({ children }) => {
  return (
    <ThemeProvider>
      <RadixThemeProvider>
        <HeaderThemeProvider>
          <div className="flex flex-col min-h-screen">{children}</div>
        </HeaderThemeProvider>
      </RadixThemeProvider>
    </ThemeProvider>
  )
}
