import type { CollectionA<PERSON><PERSON><PERSON>eHook, CollectionAfterDeleteHook } from 'payload'

import { revalidatePath, revalidateTag } from 'next/cache'

export const revalidateNode: CollectionAfterChangeHook = ({
  doc,
  previousDoc,
  req: { payload, context },
}) => {
  if (!context.disableRevalidate) {
    if (doc._status === 'published') {
      const path = `/nodes/${doc.slug}`

      payload.logger.info(`Revalidating node at path: ${path}`)

      revalidatePath(path)
      revalidateTag('nodes-sitemap')
    }

    // If the node was previously published, we need to revalidate the old path
    if (previousDoc?._status === 'published' && doc._status !== 'published') {
      const oldPath = `/nodes/${previousDoc.slug}`

      payload.logger.info(`Revalidating old node at path: ${oldPath}`)

      revalidatePath(oldPath)
      revalidateTag('nodes-sitemap')
    }
  }
  return doc
}

export const revalidateDelete: CollectionAfterDeleteHook = ({ doc, req: { context } }) => {
  if (!context.disableRevalidate) {
    const path = `/nodes/${doc?.slug}`

    revalidatePath(path)
    revalidateTag('nodes-sitemap')
  }

  return doc
}
