import type { GlobalAfterChangeHook } from 'payload'

import { revalidatePath, revalidateTag } from 'next/cache'

export const revalidateLexicon: GlobalAfterChangeHook = ({ doc, req: { payload, context } }) => {
  if (!context.disableRevalidate) {
    payload.logger.info(`Revalidating lexicon`)

    // Revalidate the lexicon page
    revalidatePath('/lexicon')
    revalidateTag('global_lexicon')
  }

  return doc
}
