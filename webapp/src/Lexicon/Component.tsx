import { getCachedGlobal } from '@/utilities/getGlobals'
import { Lexicon } from '@/payload-types'

export async function getLexiconData(): Promise<Lexicon> {
  try {
    // This will be properly typed as Lexicon
    return await getCachedGlobal('lexicon')()
  } catch (error) {
    console.error('Error fetching lexicon data:', error)
    return {
      id: 0,
      title: 'Lexicon',
      description: 'A comprehensive list of all terms and definitions.',
      meta: {
        title: 'Lexicon - Geopolitica',
        description: 'Browse our comprehensive lexicon of geopolitical terms and concepts.',
      },
    }
  }
}
