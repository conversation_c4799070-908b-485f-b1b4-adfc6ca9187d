{"npm.packageManager": "pnpm", "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "editor.formatOnSaveMode": "file", "typescript.tsdk": "node_modules/typescript/lib", "[javascript][typescript][typescriptreact]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}}