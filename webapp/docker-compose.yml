version: '3'

services:
  payload:
    image: node:18-alpine
    ports:
      - '3000:3000'
    volumes:
      - .:/home/<USER>/app
      - node_modules:/home/<USER>/app/node_modules
    working_dir: /home/<USER>/app/
    command: sh -c "yarn install && yarn dev"
    depends_on:
      - postgresql
    env_file:
      - .env

  postgresql:
    image: postgres:16
    restart: unless-stopped
    environment:
      - POSTGRES_PASSWORD=023jcds83HF
      - POSTGRES_DB=geopolitica
    ports:
      - "5432:5432"
    volumes:
      - db:/var/lib/postgresql/data

volumes:
  db:
  node_modules:
